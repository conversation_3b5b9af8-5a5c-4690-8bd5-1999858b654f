import FlowStacks
import SwiftUI

// MARK: - 支付视图

struct PaymentView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var authStore: AuthStore
    @State private var selectedTab = 0 // 0: 至尊版(Ultra), 1: 专业版(Plus), 2: 口袋版(Pro)
    var showNavigationBar = true
    var onBack: (() -> Void)?

    private let membershipTypes: [(name: String, productType: String, themeColor: Color, gradientColors: [Color])] = [
        (
            name: "至尊版",
            productType: "ULTRA",
            themeColor: Color(hex: "#C83CF3"),
            gradientColors: [
                Color(hex: "#BA6EFD"), // 淡紫色 (0%)
                Color(hex: "#DF8BFF"), // 粉紫色 (39%)
                Color(hex: "#F8ADFF"), // 浅粉紫 (51%)
                Color(hex: "#D885FD"), // 中紫色 (64%)
                Color(hex: "#BB6EFD"), // 深紫色 (100%)
            ]
        ),
        (
            name: "专业版",
            productType: "PLUS",
            themeColor: Color(hex: "#64EC59"),
            gradientColors: [
                Color(hex: "#64EC59"), // 主绿色
                Color(hex: "#A4F631"), // 亮绿色
                Color(hex: "#CAFC1A"), // 黄绿色
                Color(hex: "#9EF535"), // 中间绿
                Color(hex: "#64EC59"), // 回到主绿色
            ]
        ),
        (
            name: "口袋版",
            productType: "PRO",
            themeColor: Color(hex: "#4A9EFF"),
            gradientColors: [
                Color(hex: "#4A9EFF"), // 主蓝色
                Color(hex: "#6DB3FF"), // 浅蓝色
                Color(hex: "#92C9FF"), // 天蓝色
                Color(hex: "#5EABFF"), // 中间蓝
                Color(hex: "#4A9EFF"), // 回到主蓝色
            ]
        ),
    ]

    var body: some View {
        VStack(spacing: 0) {
            if showNavigationBar {
                MembershipNavigationBar(
                    selectedTab: $selectedTab,
                    tabs: membershipTypes.map { (
                        name: $0.name,
                        themeColor: $0.themeColor,
                        gradientColors: $0.gradientColors
                    ) },
                    onBack: handleBackTap
                )
            }
            paymentContent
        }
        .background(backgroundGradient)
        .toolbar(showNavigationBar ? .hidden : .automatic)
    }

    @ViewBuilder
    private var backgroundGradient: some View {
        let currentTheme = membershipTypes[selectedTab]

        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: DesignSystem.Colors.membershipPurchaseBackground, location: 0.0),
                .init(color: currentTheme.themeColor.opacity(0.1), location: 0.3),
                .init(color: currentTheme.themeColor.opacity(0.05), location: 0.7),
                .init(color: DesignSystem.Colors.membershipPurchaseBackground, location: 1.0),
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea()
        .animation(.easeInOut(duration: 0.5), value: selectedTab)
    }

    @ViewBuilder
    private var paymentContent: some View {
        ProductPurchaseView(selectedTab: $selectedTab, membershipTypes: membershipTypes)
    }

    private func handleBackTap() {
        if let onBack {
            onBack()
        } else {
            navigator.goBack()
        }
    }
}
