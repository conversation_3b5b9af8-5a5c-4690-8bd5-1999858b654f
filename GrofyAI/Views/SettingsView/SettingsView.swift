import FlowStacks
import MijickPopups
import SwiftUI

// MARK: - 设置界面

struct SettingsView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject private var authStore: AuthStore
    @State private var showLogoutAlert = false
    @State private var showDeleteAccountAlert = false
    @State private var isDeleting = false
    @State private var isShowingShareSheet = false
    private let deleteAccountService = DeleteAccountService()

    var body: some View {
        VStack(spacing: 0) {
            settingsContent
        }
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackTap)
            }
            ToolbarItem(placement: .principal) {
                Text("设置")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .overlay(
            Group {
                if isDeleting {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()
                        .overlay(
                            VStack(spacing: 16) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(1.2)
                                Text("正在注销账号...")
                                    .foregroundColor(.white)
                                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                            }
                            .padding(24)
                            .background(Color.black.opacity(0.8))
                            .cornerRadius(12)
                        )
                }
            }
        )
        .alert("退出登录", isPresented: $showLogoutAlert) {
            Button("取消", role: .cancel) {}
            Button("确定", role: .destructive) {
                logout()
            }
        } message: {
            Text("确定要退出登录吗？")
        }
    }

    // MARK: - 设置内容

    @ViewBuilder
    private var settingsContent: some View {
        List {
            Section(header: Text("通用")) {
                Button(action: {
                    navigator.push(Route.themeSetting)
                }) {
                    HStack {
                        Image(systemName: "paintbrush")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("主题设置")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                Button(action: {
                    handleLanguageSettingTap()
                }) {
                    HStack {
                        Image(systemName: "globe")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("语言设置")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            Section(header: Text("使用记录")) {
                Button(action: {
                    navigator.push(Route.imageRecognitionHistory)
                }) {
                    HStack {
                        Image(systemName: "camera")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("识图历史")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                Button(action: {
                    navigator.push(Route.creditsHistory)
                }) {
                    HStack {
                        Image(systemName: "creditcard")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("积分消耗")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            Section(header: Text("支持")) {
                Button(action: {
                    navigator.push(Route.problemFeedback)
                }) {
                    HStack {
                        Image(systemName: "exclamationmark.bubble")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("问题反馈")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                Button(action: {
                    AppStoreHelper.openAppStoreWriteReviewPage()
                }) {
                    HStack {
                        Image(systemName: "star")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("应用评分")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                Button(action: {
                    isShowingShareSheet = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("分享应用")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .sheet(isPresented: $isShowingShareSheet) {
                    // 获取要分享的内容
                    let shareText = "快来试试这款超棒的应用吧！"

                    // 2.你的 App Store链接（重要：请替换成你自己的AppID
                    // 格式：https://apps.apple.com/app/id<你的AppID>
                    let appURL = URL(string: AppConfig.Store.AppUrl)! // 示例ID
                    ShareView(activityItems: [shareText, appURL])
                }

                Button(action: {
                    navigator.push(Route.aboutUs)
                }) {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("关于我们")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            Section(header: Text("隐私与安全")) {
                Button(action: {
                    handleTermsOfServiceTap()
                }) {
                    HStack {
                        Image(systemName: "doc.text")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("使用条款")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                Button(action: {
                    handlePrivacyPolicyTap()
                }) {
                    HStack {
                        Image(systemName: "hand.raised")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 16, weight: .medium))
                            .frame(width: 20)
                        Text("隐私政策")
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)

                if authStore.getAccessToken() != nil {
                    Button(action: {
                        showDeleteAccountPopup()
                    }) {
                        HStack {
                            Image(systemName: "person.crop.circle.badge.minus")
                                .foregroundColor(DesignSystem.Colors.error)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("注销账号")
                                .foregroundStyle(DesignSystem.Colors.error)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                    Button(action: {
                        showLogoutAlert = true
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 16, weight: .medium))
                                .frame(width: 20)
                            Text("退出登录")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .font(.system(size: 14, weight: .medium))
                        }
                    }
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
        .scrollContentBackground(.hidden)
        .background(DesignSystem.Colors.backgroundPage)
    }

    private func handleBackTap() {
        navigator.pop()
    }

    private func logout() {
        authStore.clearUser()
        ToastManager.shared.showSuccess("已退出登录")
    }

    @MainActor
    private func deleteAccount() async {
        isDeleting = true

        do {
            try await deleteAccountService.deleteAccount()

            ToastManager.shared.showSuccess("账号已注销")
            authStore.clearUser()
            navigator.popToRoot()

        } catch let error as BusinessError {
            ToastManager.shared.showError(error.message.isEmpty ? "注销失败，请稍后重试" : error.message)
        } catch {
            ToastManager.shared.showError("注销失败，请稍后重试")
        }

        isDeleting = false
    }

    private func handleTermsOfServiceTap() {
        WebViewManager.shared.openTermsOfService()
    }

    private func handlePrivacyPolicyTap() {
        WebViewManager.shared.openPrivacyPolicy()
    }

    private func handleLanguageSettingTap() {
        openLanguageSettings()
    }

    private func openLanguageSettings() {
        guard let settingsURL = URL(string: UIApplication.openSettingsURLString) else {
            return
        }

        if UIApplication.shared.canOpenURL(settingsURL) {
            UIApplication.shared.open(settingsURL, options: [:], completionHandler: nil)
        }
    }

    private func showDeleteAccountPopup() {
        Task {
            await DeleteAccountConfirmPopup(
                onDelete: {
                    Task {
                        await deleteAccount()
                    }
                }
            )
            .present()
        }
    }
}
