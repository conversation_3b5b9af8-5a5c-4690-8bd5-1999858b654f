import Combine
import FlowStacks
import Kingfisher
import MarkdownView
import MijickPopups
import SwiftUI

// MARK: - 文件对话页面

struct FileChatView: View {
    let knowledgeId: Int
    let fileName: String?
    let initialMessage: String?
    let threadId: String?

    @StateObject private var fileChatController = FileChatController()
    @EnvironmentObject var navigator: FlowPathNavigator
    @FocusState private var isInputFocused: Bool

    // 内部状态
    @State private var isAtBottom = true
    @State private var isInitialLoad = true
    @State private var isUserScrolling = false

    init(knowledgeId: Int, fileName: String? = nil, initialMessage: String? = nil, threadId: String? = nil) {
        self.knowledgeId = knowledgeId
        self.fileName = fileName
        self.initialMessage = initialMessage
        self.threadId = threadId
    }

    var body: some View {
        ScrollViewReader { proxy in
            VStack(spacing: 0) {
                // 文件分析标签选择器
                if fileChatController.hasFileAnalysisMessage {
                    fileAnalysisTabSelector
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.bottom, DesignSystem.Spacing.sm)
                        .background(DesignSystem.Colors.backgroundPage)
                }

                // 聊天内容区域
                ScrollView {
                    chatContent
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .scrollDetector(isAtBottom: $isAtBottom, isUserScrolling: $isUserScrolling)

                    BottomDetector(isAtBottom: $isAtBottom)
                        .id("bottom-anchor")
                }
                .simultaneousGesture(
                    fileChatController.hasFileAnalysisMessage ?
                        DragGesture(minimumDistance: 20)
                        .onEnded { value in
                            handleTabSwipeGesture(value)
                        } : nil
                )
                .scrollDismissesKeyboard(.interactively)
                .overlay(alignment: .bottom) {
                    if !isAtBottom {
                        BackToBottomButton {
                            scrollToBottom(proxy: proxy, animated: false)
                        }
                        .padding(.bottom, DesignSystem.Spacing.sm)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .coordinateSpace(name: "scroll")
                .onAppear {
                    performInitialScrollToBottom(proxy: proxy)
                }
                .onChange(of: fileChatController.stableMessages.count) { newCount in
                    if isInitialLoad, newCount > 0 {
                        DispatchQueue.main.async {
                            scrollToBottom(proxy: proxy, animated: false)
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isInitialLoad = false
                        }
                    }

                    if !isInitialLoad {
                        performAutoScroll(proxy: proxy)
                    }
                }
                .onChange(of: fileChatController.streamingContent) { _ in
                    if !isInitialLoad, isAtBottom, !isUserScrolling {
                        performStreamingScroll(proxy: proxy)
                    }
                }
                .onChange(of: isInputFocused) { newValue in
                    if newValue, isAtBottom, !isUserScrolling {
                        DispatchQueue.main.asyncAfter(deadline: .now()) {
                            scrollToBottom(proxy: proxy, animated: true)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .layoutPriority(1)
                .contentShape(Rectangle())
                .onTapGesture {
                    if isInputFocused {
                        isInputFocused = false
                    }
                }

                // 输入栏
                FileChatInputBar(
                    inputText: $fileChatController.inputText,
                    isInputFocused: $isInputFocused,
                    isLoading: fileChatController.isChatLoading,
                    placeholder: getInputPlaceholder(),
                    onSend: { _ in
                        fileChatController.sendMessage()
                        scrollToBottom(proxy: proxy, animated: false)
                    },
                    onStop: {
                        fileChatController.stopStreaming()
                    }
                )
                .layoutPriority(0)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(DesignSystem.Colors.backgroundPage)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    toolbarLeadingItem()
                }
                ToolbarItem(placement: .topBarTrailing) {
                    toolbarTrailingItem()
                }
                ToolbarItem(placement: .principal) {
                    toolbarPrincipalItem()
                }
            }
            .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
            .navigationBarBackButtonHidden()
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                fileChatController.setFileInfo(knowledgeId: knowledgeId, fileName: fileName)

                if let threadId {
                    fileChatController.loadHistoryChat(threadId: threadId)
                } else {
                    fileChatController.startInitialAnalysis()
                }
            }
            .onDisappear {
                fileChatController.clearState()
                ToastManager.shared.clearToast()
            }
            .onReceive(NotificationCenter.default.publisher(for: .fileChatSwitchToHistory)) { notification in
                handleHistorySwitchNotification(notification)
            }
            .task {
                if let initialMessage, !initialMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    try? await Task.sleep(nanoseconds: 100_000_000)
                    fileChatController.inputText = initialMessage
                    fileChatController.sendMessage()
                }
            }
            .requireAuthenticationWithNavigation("请先登录以使用文件对话功能") {
                navigator.pop()
            }
        }
    }

    // MARK: - 文件分析标签选择器

    @ViewBuilder
    private var fileAnalysisTabSelector: some View {
        if let firstMessage = fileChatController.analysisMessage,
           let analysisResult = firstMessage.fileAnalysisResult
        {
            FileAnalysisTabSelector(
                selectedTab: Binding(
                    get: { fileChatController.selectedTab },
                    set: { fileChatController.switchAnalysisTab(to: $0) }
                ),
                analysisResult: analysisResult
            )
        }
    }

    // MARK: - 聊天内容

    @ViewBuilder
    private var chatContent: some View {
        LazyVStack(spacing: 14) {
            // 文件分析消息（如果存在）
            if let analysisMessage = fileChatController.analysisMessage {
                ChatItem(
                    message: analysisMessage,
                    isLoading: fileChatController.isAnalysisLoading,
                    onRetry: nil,
                    onCopy: { fileChatController.copyMessage(analysisMessage) },
                    onShare: { fileChatController.shareMessage(analysisMessage) },
                    onVariantChanged: { _ in },
                    variantInfo: nil
                )
                .id(analysisMessage.id)
            }

            // 稳定消息列表
            ForEach(fileChatController.stableMessages) { message in
                if !fileChatController.isRetryingMessage(messageId: message.id) {
                    ChatItem(
                        message: message,
                        isLoading: false,
                        onRetry: shouldShowRetryButton(for: message) ?
                            { fileChatController.retryMessage(messageId: message.id) } : nil,
                        onCopy: { fileChatController.copyMessage(message) },
                        onShare: { fileChatController.shareMessage(message) },
                        onVariantChanged: { variantIndex in fileChatController.switchMessageVariant(
                            messageId: message.id,
                            variantIndex: variantIndex
                        ) },
                        variantInfo: fileChatController.getMessageVariantInfo(messageId: message.id)
                    )
                    .id(message.id)
                }
            }

            // 流式消息
            streamingMessageContent

            // 底部锚点
            EmptyView()
        }
    }

    @ViewBuilder
    private var streamingMessageContent: some View {
        if let streaming = fileChatController.streamingMessage {
            let variantInfo = fileChatController.isCurrentlyRetrying() ?
                fileChatController.getRetryVariantInfo() : nil

            ChatItem(
                message: streaming,
                isLoading: fileChatController.isChatLoading,
                onRetry: nil,
                onCopy: { fileChatController.copyMessage(streaming) },
                onShare: { fileChatController.shareMessage(streaming) },
                onVariantChanged: { variantIndex in
                    if let retryingId = fileChatController.currentRetryingMessageId {
                        fileChatController.switchMessageVariant(
                            messageId: retryingId,
                            variantIndex: variantIndex
                        )
                    }
                },
                variantInfo: variantInfo,
                streamingContent: fileChatController.streamingContent.isEmpty ? nil : fileChatController
                    .streamingContent
            )
            .id(streaming.id)
            .transition(.opacity.combined(with: .move(edge: .bottom)))
        }
    }

    // MARK: - Toolbar Items

    @ViewBuilder
    private func toolbarLeadingItem() -> some View {
        BackButton(onBack: handleBackTap)
    }

    @ViewBuilder
    private func toolbarTrailingItem() -> some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            HistoryButton(onTap: handleHistoryTap)
            NewChatButton(onTap: handleNewChatTap)
        }
    }

    @ViewBuilder
    private func toolbarPrincipalItem() -> some View {
        Text("文件对话")
            .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    // MARK: - Event Handlers

    private func handleBackTap() {
        navigator.pop()
    }

    private func handleNewChatTap() {
        fileChatController.clearState()
        fileChatController.startInitialAnalysis()
    }

    private func handleHistoryTap() {
        navigator.push(Route.chatHistory(chatMode: .rag))
    }

    private func handleHistorySwitchNotification(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let knowledgeId = userInfo["knowledgeId"] as? Int,
              let threadId = userInfo["threadId"] as? String,
              let fileName = userInfo["fileName"] as? String
        else {
            return
        }

        fileChatController.switchToHistoryRecord(
            knowledgeId: knowledgeId,
            threadId: threadId,
            fileName: fileName
        )
    }

    private func handleTabSwipeGesture(_ value: DragGesture.Value) {
        let horizontalMovement = value.translation.width
        let verticalMovement = abs(value.translation.height)

        guard abs(horizontalMovement) > 50,
              abs(horizontalMovement) > verticalMovement * 2.0,
              verticalMovement < 30
        else {
            return
        }

        let allTabs = FileAnalysisTab.allCases
        guard let currentIndex = allTabs.firstIndex(of: fileChatController.selectedTab) else { return }

        withAnimation(.easeInOut(duration: 0.2)) {
            if horizontalMovement > 0 {
                if currentIndex > 0 {
                    fileChatController.switchAnalysisTab(to: allTabs[currentIndex - 1])
                }
            } else {
                if currentIndex < allTabs.count - 1 {
                    fileChatController.switchAnalysisTab(to: allTabs[currentIndex + 1])
                }
            }
        }
    }

    // MARK: - Helper Methods

    private func getInputPlaceholder() -> String {
        if fileChatController.isAnalysisLoading {
            return "正在分析文件..."
        } else if fileChatController.isChatLoading {
            return "AI正在回复..."
        } else if fileChatController.isLoading {
            return "正在加载..."
        } else {
            return "询问任何与文件相关的话题"
        }
    }

    private func shouldShowRetryButton(for message: ChatMessageModel) -> Bool {
        guard !message.isUser else { return false }

        if fileChatController.streamingMessage != nil {
            if fileChatController.isCurrentlyRetrying() {
                return !fileChatController.isRetryingMessage(messageId: message.id) &&
                    isLastAIMessage(message)
            } else {
                return false
            }
        }

        return isLastAIMessage(message)
    }

    private func isLastAIMessage(_ message: ChatMessageModel) -> Bool {
        guard !message.isUser else { return false }

        if let lastAIMessage = fileChatController.stableMessages.last(where: { !$0.isUser }) {
            return lastAIMessage.id == message.id
        }

        return false
    }

    // MARK: - Scroll Management

    private func scrollToBottom(proxy: ScrollViewProxy, animated: Bool) {
        let anchorID = "bottom-anchor"

        DispatchQueue.main.async {
            let scrollAction = {
                proxy.scrollTo(anchorID, anchor: .bottom)
            }

            if animated {
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollAction()
                }
            } else {
                scrollAction()
            }
        }
    }

    private func performInitialScrollToBottom(proxy: ScrollViewProxy) {
        func checkDataAndScroll(attempt: Int = 1) {
            let hasMessages = fileChatController.hasFileAnalysisMessage ||
                !fileChatController.stableMessages.isEmpty ||
                fileChatController.streamingMessage != nil

            if hasMessages {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05 * Double(attempt)) {
                    scrollToBottom(proxy: proxy, animated: false)
                }
            } else if attempt < 10 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    checkDataAndScroll(attempt: attempt + 1)
                }
            }
        }

        checkDataAndScroll()
    }

    private func performAutoScroll(proxy: ScrollViewProxy) {
        if let lastMessage = fileChatController.stableMessages.last, lastMessage.isUser {
            scrollToBottom(proxy: proxy, animated: false)
            return
        }

        if isAtBottom, !isUserScrolling {
            scrollToBottom(proxy: proxy, animated: true)
        }
    }

    private func performStreamingScroll(proxy: ScrollViewProxy) {
        let anchorID = "bottom-anchor"
        proxy.scrollTo(anchorID, anchor: .bottom)
    }
}

// MARK: - 文件对话输入栏

private struct FileChatInputBar: View {
    @Binding var inputText: String
    @FocusState.Binding var isInputFocused: Bool
    let isLoading: Bool
    let placeholder: String
    let onSend: (String) -> Void
    let onStop: () -> Void

    @State private var cachedIsSendEnabled = false

    // MARK: - 语音功能状态管理

    @State private var inputState = InputState()
    @Namespace private var inputTransition

    // 语音服务
    @EnvironmentObject private var sttService: SpeechRecognitionService
    @StateObject private var audioManager = AudioLevelManager()
    @ObservedObject private var toastManager = ToastManager.shared

    // 认证相关环境对象
    @EnvironmentObject private var authStore: AuthStore
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.xs)

            // 底部安全区域占位
            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
        .onChange(of: inputText) { newText in
            let newIsEnabled = !newText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            if newIsEnabled != cachedIsSendEnabled {
                cachedIsSendEnabled = newIsEnabled
            }
        }
        .onChange(of: sttService.recognizedText) { newText in
            if inputState.isInVoiceMode, !newText.isEmpty {
                inputState.updateVoiceText(newText)
            }
        }
        .onChange(of: sttService.state) { newState in
            if inputState.isInVoiceMode {
                switch newState {
                case .listening:
                    inputState.startRecording()
                case .error, .idle:
                    if !inputState.isManuallyExiting {
                        inputState.stopRecording()
                        handleSpeechServiceStateChange()
                    }
                default:
                    break
                }
            }
        }
        .onDisappear {
            cleanupVoiceResources()
        }
    }

    @ViewBuilder
    private var inputContainerView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            if inputState.voice == .active {
                VoiceWaveDisplay(
                    audioManager: audioManager,
                    onClose: handleExitVoiceMode,
                    onConfirm: handleConfirmVoiceInput,
                    isProcessing: inputState.isConfirming
                )
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.lg)
                .frame(minHeight: 44)
            } else {
                textInputArea
            }

            bottomButtonRow
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    @ViewBuilder
    private var textInputArea: some View {
        TextField(placeholder, text: $inputText, axis: .vertical)
            .fontLG(weight: DesignSystem.FontWeight.regular)
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .focused($isInputFocused)
            .lineLimit(2...6)
            .textFieldStyle(.plain)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.bottom, DesignSystem.Spacing.xs)
            .frame(minHeight: 44, alignment: .topLeading)
            .fixedSize(horizontal: false, vertical: true)
            .contentShape(Rectangle())
            .onTapGesture {
                handleTextFieldTap()
            }
            .disabled(inputState.isInVoiceMode)
            .accessibilityAddTraits(.isButton)
            .accessibilityLabel("文件对话输入框")
            .accessibilityHint("点击开始与文件对话")
    }

    @ViewBuilder
    private var bottomButtonRow: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.md) {
            leftButtonGroup

            Spacer()

            if isLoading {
                Button(action: onStop) {
                    Image(systemName: "stop.circle.fill")
                        .foregroundColor(.red)
                        .titleSmallStyle()
                }
                .buttonStyle(.plain)
            } else {
                FileChatSendButton(inputText: $inputText, action: handleSendAction)
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.md)
        .opacity(inputState.isInVoiceMode ? 0.4 : 1.0)
        .allowsHitTesting(!inputState.isInVoiceMode)
    }

    @ViewBuilder
    private var leftButtonGroup: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            if inputText.isEmpty, !inputState.isInVoiceMode {
                InputButton(iconName: "IconInputMicrophone", action: handleEnterVoiceMode)
            }
        }
    }

    private func handleSendAction() {
        guard cachedIsSendEnabled, !isLoading else { return }

        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        onSend(trimmedText)
        inputText = ""
        isInputFocused = false
        cachedIsSendEnabled = false
    }

    // MARK: - 语音功能方法

    /// 检查认证状态
    private func checkAuthenticationRequired() -> Bool {
        guard AuthStore.shared.hasValidToken() else {
            globalAuthManager.requestAuthentication()
            return false
        }
        return true
    }

    /// 进入语音模式
    private func handleEnterVoiceMode() {
        guard checkAuthenticationRequired() else { return }

        withAnimation(.easeOut(duration: 0.2)) {
            inputState.switchToVoiceMode()
        }

        // 启动音频监控
        audioManager.startMonitoring()

        Task {
            do {
                try await sttService.startContinuousRecognition()
            } catch {
                toastManager.showError("语音识别启动失败")
                // 如果启动失败，退出语音模式
                withAnimation(.easeOut(duration: 0.2)) {
                    inputState.exitVoiceMode()
                }
                audioManager.stopMonitoring()
            }
        }
    }

    /// 退出语音模式
    private func handleExitVoiceMode() {
        withAnimation(.easeOut(duration: 0.2)) {
            inputState.exitVoiceMode()
        }

        // 停止音频监控
        audioManager.stopMonitoring()

        Task.detached {
            await sttService.stopRecognition()

            await MainActor.run {
                inputState.finishConfirming()
            }
        }
    }

    /// 确认语音输入
    private func handleConfirmVoiceInput() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputText = recognizedText
            handleSendAction()
        }
        handleExitVoiceMode()
    }

    /// 处理语音服务状态变化
    private func handleSpeechServiceStateChange() {
        guard !inputState.isManuallyExiting else { return }

        if !sttService.isListening {
            if !inputState.isConfirming {
                let currentRecognizedText = sttService.recognizedText
                if inputState.isInVoiceMode, !currentRecognizedText.isEmpty {
                    handleSpeechRecognitionSuccess()
                }
            }
        }
    }

    /// 处理语音识别成功
    private func handleSpeechRecognitionSuccess() {
        let recognizedText = sttService.getRecognizedTextAndClear()
        if !recognizedText.isEmpty {
            inputState.updateVoiceText(recognizedText)
            inputState.voice = .active
        }
    }

    /// 清理语音资源
    private func cleanupVoiceResources() {
        if inputState.isInVoiceMode {
            inputState.isManuallyExiting = true
            audioManager.stopMonitoring()
            Task.detached {
                await sttService.stopRecognition()

                await MainActor.run {
                    inputState.exitVoiceMode()
                    inputState.finishConfirming()
                }
            }
        }
    }

    private func handleTextFieldTap() {
        guard !isInputFocused else { return }

        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        withAnimation(.easeInOut(duration: 0.2)) {
            isInputFocused = true
        }
    }
}

// MARK: - 发送按钮组件

private struct FileChatSendButton: View {
    let action: () -> Void
    @Binding var inputText: String

    @State private var cachedIsEnabled = false
    @State private var cachedForegroundColor: Color
    @State private var cachedOpacity = 0.4

    init(inputText: Binding<String>, action: @escaping () -> Void) {
        _inputText = inputText
        self.action = action

        let isEnabled = !inputText.wrappedValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        _cachedIsEnabled = State(initialValue: isEnabled)
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors
            .textSecondary
        )
        _cachedOpacity = State(initialValue: isEnabled ? 1.0 : 0.4)
    }

    var body: some View {
        Button(action: action) {
            Image("IconInputSend")
                .foregroundColor(cachedForegroundColor)
        }
        .buttonStyle(.plain)
        .disabled(!cachedIsEnabled)
        .opacity(cachedOpacity)
        .onChange(of: inputText) { text in
            let newIsEnabled = !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            if newIsEnabled != cachedIsEnabled {
                cachedIsEnabled = newIsEnabled
                cachedForegroundColor = newIsEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary
                cachedOpacity = newIsEnabled ? 1.0 : 0.4
            }
        }
    }
}

// MARK: - 文件分析分段控制器

private struct FileAnalysisTabSelector: View {
    @Binding var selectedTab: FileAnalysisTab
    let analysisResult: FileAnalysisResult

    var body: some View {
        Picker("分析内容", selection: $selectedTab) {
            ForEach(FileAnalysisTab.allCases) { tab in
                Text(tab.displayName)
                    .tag(tab)
            }
        }
        .pickerStyle(.segmented)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.sm)
    }
}
