import FlowStacks
import King<PERSON>er
import MijickPopups
import SwiftUI

// MARK: - 图片对话页面

struct ImageChatView: View {
    let initialMessage: String?
    let threadId: String?

    @StateObject private var imageChatController = ImageChatController()
    @EnvironmentObject var navigator: FlowPathNavigator
    @FocusState private var isInputFocused: Bool

    // 内部状态
    @State private var isAtBottom = true
    @State private var isInitialLoad = true
    @State private var isUserScrolling = false
    @State private var showParameters = false
    @State private var shouldHideThumbnails = false
    @State private var showPrologue = true

    init(initialMessage: String? = nil, threadId: String? = nil) {
        self.initialMessage = initialMessage
        self.threadId = threadId
    }

    var body: some View {
        ScrollViewReader { proxy in
            VStack(spacing: 0) {
                ScrollView {
                    chatContent
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .scrollDetector(isAtBottom: $isAtBottom, isUserScrolling: $isUserScrolling)

                    BottomDetector(isAtBottom: $isAtBottom)
                        .id("bottom-anchor")
                }
                .scrollDismissesKeyboard(.interactively)
                .overlay(alignment: .bottom) {
                    if !isAtBottom {
                        BackToBottomButton {
                            scrollToBottom(proxy: proxy, animated: false)
                        }
                        .padding(.bottom, DesignSystem.Spacing.sm)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .coordinateSpace(name: "scroll")
                .onAppear {
                    performInitialScrollToBottom(proxy: proxy)
                }
                .onChange(of: imageChatController.stableMessages.count) { newCount in
                    if isInitialLoad, newCount > 0 {
                        DispatchQueue.main.async {
                            scrollToBottom(proxy: proxy, animated: false)
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isInitialLoad = false
                        }
                    }

                    if !isInitialLoad {
                        performAutoScroll(proxy: proxy)
                    }
                }
                .onChange(of: imageChatController.streamingContent) { _ in
                    if !isInitialLoad, isAtBottom, !isUserScrolling {
                        performStreamingScroll(proxy: proxy)
                    }
                }
                .onChange(of: isInputFocused) { newValue in
                    if newValue, isAtBottom, !isUserScrolling {
                        DispatchQueue.main.asyncAfter(deadline: .now()) {
                            scrollToBottom(proxy: proxy, animated: true)
                        }
                    }
                }
                .onChange(of: showParameters) { showParams in
                    if showParams, isInputFocused {
                        isInputFocused = false
                    }
                }
                .frame(maxWidth: .infinity)
                .layoutPriority(1)
                .contentShape(Rectangle())
                .onTapGesture {
                    if isInputFocused {
                        isInputFocused = false
                    }
                    if showParameters {
                        showParameters = false
                    }
                }

                // 参数配置视图
                ImageGenerationParametersView(
                    parameters: $imageChatController.imageGenerationParameters,
                    isVisible: $showParameters,
                    onParameterChange: { parameters in
                        imageChatController.updateImageGenerationParameters(parameters)
                    }
                )

                ImageChatInputBar(
                    inputText: $imageChatController.inputText,
                    parameters: $imageChatController.imageGenerationParameters,
                    uploadedImages: imageChatController.uploadedImageUrlsBinding,
                    isInputFocused: $isInputFocused,
                    threadId: ThreadManager.shared.getCurrentThreadId(),
                    showParameters: $showParameters,
                    shouldHideThumbnails: shouldHideThumbnails,
                    isLoading: imageChatController.isLoading,
                    placeholder: "描述您想要生成的图片...",
                    onSend: { _ in
                        shouldHideThumbnails = true
                        imageChatController.sendMessage()
                        scrollToBottom(proxy: proxy, animated: false)
                    },
                    onImageFileUpload: { uploadResult in
                        imageChatController.addUploadedImageFile(uploadResult)
                    },
                    onImageRemove: { imageUrl in
                        imageChatController.removeUploadedImageUrl(imageUrl)
                    },
                    onMicrophoneAction: {},
                    onParameterChange: { parameters in
                        imageChatController.updateImageGenerationParameters(parameters)
                    },
                    onStop: {
                        imageChatController.stopStreaming()
                    }
                )
                .layoutPriority(0)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(DesignSystem.Colors.backgroundPage)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    toolbarLeadingItem()
                }
                ToolbarItem(placement: .topBarTrailing) {
                    toolbarTrailingItem()
                }
                ToolbarItem(placement: .principal) {
                    toolbarPrincipalItem()
                }
            }
            .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
            .navigationBarBackButtonHidden()
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            if let threadId {
                imageChatController.loadHistoryChat(threadId: threadId)
            }
            showPrologue = !ProloguePreferences.shared.isImageChatPrologueHidden
        }
        .onDisappear {
            imageChatController.stopStreaming()
            imageChatController.resetCurrentThread()
            ToastManager.shared.clearToast()
        }
        .task {
            if let initialMessage, !initialMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                imageChatController.processInitialMessage(initialMessage)
            }
        }
        .onChange(of: imageChatController.shouldClearUploadedImages) { shouldClear in
            if shouldClear {
                imageChatController.clearUploadedImages()
                shouldHideThumbnails = false
                imageChatController.shouldClearUploadedImages = false
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .imageChatSwitchToHistory)) { notification in
            handleHistorySwitchNotification(notification)
        }
    }

    // MARK: - 聊天内容

    @ViewBuilder
    private var chatContent: some View {
        LazyVStack(spacing: 14) {
            if showPrologue {
                Prologue(
                    text: "✨ 两种方式创作图片：\n\n💬 文字描述：直接告诉我你想要的画面，比如\"夕阳下的海边小屋\"\n\n📸 参考图片：上传图片后描述想要的变化，我会基于你的图片创作新作品",
                    onDismiss: {
                        ProloguePreferences.shared.hideImageChatPrologue()
                        showPrologue = false
                    }
                )
                .padding(.horizontal, DesignSystem.Spacing.md)
                .transition(.asymmetric(
                    insertion: .opacity.combined(with: .scale(scale: 0.95, anchor: .top)),
                    removal: .opacity.combined(with: .scale(scale: 0.95, anchor: .top))
                ))
            }

            ForEach(imageChatController.stableMessages) { message in
                if !imageChatController.isRetryingMessage(messageId: message.id) {
                    ChatItem(
                        message: message,
                        isLoading: shouldShowLoadingForMessage(message),
                        onRetry: shouldShowRetryButton(for: message) ?
                            { imageChatController.retryMessage(messageId: message.id) } : nil,
                        onCopy: {},
                        onShare: {},
                        onVariantChanged: { variantIndex in imageChatController.switchMessageVariant(
                            messageId: message.id,
                            variantIndex: variantIndex
                        ) },
                        variantInfo: imageChatController.getMessageVariantInfo(messageId: message.id)
                    )
                    .id(message.id)
                }
            }

            streamingMessageContent
            bottomAnchorContent
        }
    }

    @ViewBuilder
    private var streamingMessageContent: some View {
        if let streaming = imageChatController.streamingMessage {
            let variantInfo = imageChatController.isCurrentlyRetrying() ?
                imageChatController.getRetryVariantInfo() : nil

            ChatItem(
                message: streaming,
                isLoading: imageChatController.isLoading,
                onRetry: nil,
                onCopy: {},
                onShare: {},
                onVariantChanged: { variantIndex in
                    if let retryingId = imageChatController.currentRetryingMessageId {
                        imageChatController.switchMessageVariant(
                            messageId: retryingId,
                            variantIndex: variantIndex
                        )
                    }
                },
                variantInfo: variantInfo,
                streamingContent: imageChatController.streamingContent.isEmpty ? nil : imageChatController
                    .streamingContent
            )
            .id(streaming.id)
            .transition(.opacity.combined(with: .move(edge: .bottom)))
        }
    }

    @ViewBuilder
    private var bottomAnchorContent: some View {
        EmptyView()
    }

    // MARK: - Toolbar Items

    @ViewBuilder
    private func toolbarLeadingItem() -> some View {
        BackButton(onBack: handleBackTap)
    }

    @ViewBuilder
    private func toolbarTrailingItem() -> some View {
        HStack(spacing: 12) {
            if !showPrologue {
                Button(action: handleRestorePrologueTap) {
                    Image(systemName: "info.circle")
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            HistoryButton(onTap: handleHistoryTap)
            NewChatButton(onTap: handleNewChatTap)
        }
    }

    @ViewBuilder
    private func toolbarPrincipalItem() -> some View {
        Text("图片生成")
            .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textPrimary)
    }

    // MARK: - 私有方法

    private func handleBackTap() {
        navigator.pop()
    }

    private func handleNewChatTap() {
        imageChatController.clearMessages()
    }

    private func handleHistoryTap() {
        navigator.push(Route.chatHistory(chatMode: .image))
    }

    private func handleRestorePrologueTap() {
        withAnimation(.easeInOut(duration: 0.3)) {
            ProloguePreferences.shared.showImageChatPrologue()
            showPrologue = true
        }
    }

    private func getLoadingText(for loadingType: ChatLoadingType) -> String {
        switch loadingType {
        case .streaming:
            return "正在生成图片..."
        case .loadingHistory:
            return "正在加载历史对话..."
        case .none, .uploading:
            return ""
        }
    }

    private func shouldShowSkeleton(for loadingType: ChatLoadingType) -> Bool {
        switch loadingType {
        case .loadingHistory:
            return true
        case .none, .streaming, .uploading:
            return false
        }
    }

    private func handleHistorySwitchNotification(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let threadId = userInfo["threadId"] as? String
        else {
            return
        }

        imageChatController.loadHistoryChat(threadId: threadId)
    }

    // MARK: - 辅助方法

    private func shouldShowRetryButton(for message: ChatMessageModel) -> Bool {
        guard !message.isUser else { return false }

        if imageChatController.streamingMessage != nil {
            if imageChatController.isCurrentlyRetrying() {
                return !imageChatController.isRetryingMessage(messageId: message.id) &&
                    isLastAIMessage(message, in: imageChatController.stableMessages)
            } else {
                return false
            }
        }

        return isLastAIMessage(message, in: imageChatController.stableMessages)
    }

    private func shouldShowLoadingForMessage(_ message: ChatMessageModel) -> Bool {
        return false
    }

    private func isLastAIMessage(_ message: ChatMessageModel, in messages: [ChatMessageModel]) -> Bool {
        guard !message.isUser else { return false }

        if let lastAIMessage = messages.last(where: { !$0.isUser }) {
            return lastAIMessage.id == message.id
        }

        return false
    }

    private func scrollToBottom(proxy: ScrollViewProxy, animated: Bool) {
        let anchorID = "bottom-anchor"

        DispatchQueue.main.async {
            let scrollAction = {
                proxy.scrollTo(anchorID, anchor: .bottom)
            }

            if animated {
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollAction()
                }
            } else {
                scrollAction()
            }
        }
    }

    private func performInitialScrollToBottom(proxy: ScrollViewProxy) {
        func checkDataAndScroll(attempt: Int = 1) {
            let hasMessages = !imageChatController.stableMessages.isEmpty || imageChatController.streamingMessage != nil

            if hasMessages {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05 * Double(attempt)) {
                    scrollToBottom(proxy: proxy, animated: false)
                }
            } else if attempt < 10 {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    checkDataAndScroll(attempt: attempt + 1)
                }
            }
        }

        checkDataAndScroll()
    }

    private func performAutoScroll(proxy: ScrollViewProxy) {
        if let lastMessage = imageChatController.stableMessages.last, lastMessage.isUser {
            scrollToBottom(proxy: proxy, animated: false)
            return
        }

        if isAtBottom, !isUserScrolling {
            scrollToBottom(proxy: proxy, animated: true)
        }
    }

    private func performStreamingScroll(proxy: ScrollViewProxy) {
        let anchorID = "bottom-anchor"
        proxy.scrollTo(anchorID, anchor: .bottom)
    }
}
