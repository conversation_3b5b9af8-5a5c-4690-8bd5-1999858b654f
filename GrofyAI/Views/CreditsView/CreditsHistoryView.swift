//
//  Untitled.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/18.
//
import FlowStacks
import SwiftUI

struct CreditsHistoryView: View {
    // 使用 @StateObject 来创建和管理 Controller 的生命周期
    @EnvironmentObject var navigator: FlowPathNavigator
    @StateObject private var controller = CreditsController()

    // 将字典的 key (日期字符串) 排序，以便列表按时间倒序显示
    private var sortedDateKeys: [String] {
        controller.groupedCredits.keys.sorted(by: >)
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack{
                VStack(alignment: .leading, spacing: 5) {
                    // 如果列表为空且不在加载中，显示提示信息
                    if controller.groupedCredits.isEmpty, !controller.isLoading {
                        Text("暂无使用记录")
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                    } else {
                        // 使用 List 来构建带分组的列表
                        VStack{
                            ScrollView(.vertical, showsIndicators: false){
                                ForEach(controller.rawConsumeList) { item in
                                    CreditsHistoryRow(item: item)
                                        .onAppear {
                                            // 当最后一个条目出现时，加载更多数据
                                            
                                            Task {
                                                if item.id == controller.rawConsumeList.last?.id {
                                                    await controller.loadMoreData()
                                                }
                                            }
                                        }
                                }
//                            // 遍历分组排序后的日期
//                            ForEach(sortedDateKeys, id: \.self) { dateKey in
//                                // 为每个日期创建一个 Section
//                                Section(
//                                    header: Text(dateKey)
//                                        .font(.system(size: DesignSystem.FontSize.sm))
//                                        .padding(.leading, -10)
//                                        .padding(.bottom, 10)
//                                ) {
//                                    // 获取该日期的所有条目
//                                    let itemsForDate = controller.groupedCredits[dateKey] ?? []
//
//                                    ForEach(itemsForDate) { item in
//                                        CreditsHistoryRow(item: item)
//                                            .onAppear {
//                                                // 当最后一个条目出现时，加载更多数据
//
//                                                Task {
//                                                    if item.id == itemsForDate.last?.id,
//                                                       dateKey == sortedDateKeys.last
//                                                    {
//                                                        await controller.loadMoreData()
//                                                    }
//                                                }
//                                            }
//                                    }
//                                }
//                            }
                                // 如果正在加载更多，在列表底部显示一个加载指示器
                                if controller.isLoading, !sortedDateKeys.isEmpty {
                                    ProgressView()
                                        .frame(maxWidth: .infinity, alignment: .center)
                                        .padding()
                                }
                            }
                            .refreshable {
                                // 添加下拉刷新功能
                                await controller.refreshData()
                            }
                        }
                    }
                    
                    // 如果是首次加载，显示一个居中的加载指示器
                    if controller.isLoading, sortedDateKeys.isEmpty {
                        ProgressView("正在加载...")
                    }
                }
                .padding(.horizontal)
            }
        }
        .frame(maxWidth:.infinity)
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                BackButton(onBack: handleBackTap)
            }
            ToolbarItem(placement: .principal) {
                Text("积分使用详情")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .onAppear {
            // 当视图首次出现时，加载数据
            if controller.groupedCredits.isEmpty {
                Task {
                    await controller.refreshData()
                }
            }
        }
        .alert("错误", isPresented: .constant(controller.errorMessage != nil), actions: {
            Button("好的", role: .cancel) {
                controller.errorMessage = nil // 关闭弹窗时清除错误信息
            }
        }, message: {
            Text(controller.errorMessage ?? "未知错误")
        })
    }

    private func handleBackTap() {
        navigator.pop()
    }
}

// MARK: - CreditsHistoryRow

struct CreditsHistoryRow: View {
    let item: CreditsConsumeRes
    
    private var creditsDetail: String {
        var details: [String] = []
        
        if let provider = item.provider, !provider.isEmpty {
            details.append(provider)
        }
        if let name = item.name, !name.isEmpty {
            details.append(name)
        }
        if let spec = item.spec, !spec.isEmpty {
            details.append(spec)
        }
        // 对于数值类型，我们将其转换为字符串
        if let duration = item.duration {
            details.append("\(duration)s") // 加上单位 "s" 提高可读性
        }
        if let quality = item.quality, !quality.isEmpty {
            details.append(quality)
        }
        
        // 4. 使用 " · " 作为分隔符，将数组中的所有元素拼接成一个字符串
        //    如果 details 数组为空（所有属性都是nil），joined 会返回一个空字符串。
        return details.joined(separator: " · ")
    }
    
    private var useTypeName: String {
        guard let typeName = item.type else {
            return "暂无消耗详情"
        }
        
        switch typeName {
        case "IMAGE": return "图像创作"
        case "VIDEO": return "视频创作"
        case "EFFECT": return "特效创作"
        default: return ""
        }
    }

    var body: some View {
        VStack{
            if item.costType == "INCREMENT" {
                incrementCreditView
            }else {
                decreaseCreditView
            }
        }
        .padding(.vertical,5)
        .padding(.horizontal)
        .background(DesignSystem.Colors.backgroundCard)
        .standardCornerRadius()
    }
    
    @ViewBuilder
    private var incrementCreditView: some View {
        HStack(alignment: .center) {
            VStack(alignment: .leading, spacing: 4) {
                Text(item.creditCostsKey ?? "购买积分")
                    .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
            }
            
            Spacer()

            VStack(alignment: .leading, spacing: 4) {
                Text(" \(item.credits ?? 0)")
                    .font(.system(size: DesignSystem.FontSize.xl, weight: .medium))
                    .foregroundStyle(DesignSystem.Colors.primary)
                    .monospacedDigit() // 让数字等宽，对齐更美观
                
                Text(item.createDate ?? "")
                    .font(.system(size: DesignSystem.FontSize.md))
                    .foregroundStyle(DesignSystem.Colors.textHint)
            }
        }
    }
    
    @ViewBuilder
    private var decreaseCreditView: some View {
        HStack(alignment: .center) {
            VStack(alignment: .leading, spacing: 4) {
                Text(useTypeName)
                    .font(.system(size: DesignSystem.FontSize.lg))
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                
                Text(creditsDetail)
                    .font(.system(size: DesignSystem.FontSize.md, weight: .medium))
                    .foregroundStyle(DesignSystem.Colors.textHint)
                    .lineLimit(1)
                    .truncationMode(.tail)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(item.credits ?? 0)")
                    .font(.system(size: DesignSystem.FontSize.xl, weight: .medium))
                    .foregroundStyle(DesignSystem.Colors.primary)
                
                Text(item.createDate ?? "2222-55-55")
                    .font(.system(size: DesignSystem.FontSize.md ))
                    .foregroundStyle(DesignSystem.Colors.textHint)
            }
        }
    }
    
}
