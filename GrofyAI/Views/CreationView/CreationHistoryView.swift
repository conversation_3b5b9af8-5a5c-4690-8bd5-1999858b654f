//
//  CreationHistory.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/21.
//
import SwiftUI
import FlowStacks


enum HistoryMode: String, CaseIterable, Identifiable {
    case image = "图片"
    case video = "视频"
    case effect  = "特效"
    
    var id: String { self.rawValue }
}

struct CreationHistoryView : View {
    @Environment(\.colorScheme) private var colorScheme
    // 1. 定义 @State 变量，但不要在这里赋初始值
    @State private var selectedMode: HistoryMode
    
    // --- 新增状态 ---
    @State private var isEditing = false // 控制是否进入选择模式
    @State private var selectedItems = Set<ArtWorksCell.ID>() // 存储选中项的ID
    
    // 2. init 方法接收外部传入的默认模式
    init(defaultModel: HistoryMode? = .image) {
        // 3. 在 init 内部，初始化 @State 的底层存储 _selectedMode
        //    使用 State(initialValue:) 创建一个新的 State 实例
        _selectedMode = State(initialValue: defaultModel ?? .image)
    }
    
    var body: some View {
        ZStack{
            if colorScheme == .light {
                VStack{
                    Color.clear
                        .background( // 使用全屏背景层替代Spacer方案
                            Image("ImageNavBackground")
                                .resizable() // 关键：使图片可缩放
                                .aspectRatio(contentMode: .fill) // 填充整个区域
                                .edgesIgnoringSafeArea(.all) // 忽略所有安全区域
                        )
                        .frame(height: safeAreaTopInset())
                    Spacer()
                }
                
            }
            
            VStack(spacing: 5) {
                CustomNavigationBarView1(isEditing: $isEditing, selectedItems: $selectedItems)
                // 顶部的可滚动标签栏
                ScrollViewReader { scrollViewProxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 20) {
                            ForEach(HistoryMode.allCases, id: \.self) { tab in
                                TabButton(title: tab.rawValue, isSelected: selectedMode == tab) {
                                    // 这里的动画只影响顶部标签栏（下划线和滚动）
                                    withAnimation(.spring()) {
                                        selectedMode = tab
                                    }
                                }
                                .id(tab)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(height: 35)
                    .background(Color.clear)
                    .onChange(of: selectedMode) { newTab in
                        withAnimation {
                            scrollViewProxy.scrollTo(newTab, anchor: .center)
                        }
                    }
                    .padding(.bottom,5)
                }
                
                // 下方的可滑动内容区域 (已优化)
                TabView(selection: $selectedMode) {
                    ForEach(HistoryMode.allCases, id: \.self) { tab in
                        ZStack {
                            getColorForTab(tab)
                                .ignoresSafeArea()
                        }
                        .scrollContentBackground(.hidden) // 隐藏默认背景
                        .background(DesignSystem.Colors.backgroundPage)
                        .tag(tab)
                    }
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                // ✅ 当 selectedTab 改变时，禁用 TabView 的动画
                .animation(nil, value: selectedMode)
            }
        }
        .ignoresSafeArea(edges: .bottom)
        .navigationBarHidden(true)
        .background(DesignSystem.Colors.backgroundPage)        
    }
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        return keyWindow?.safeAreaInsets.top ?? 0
    }
    
    @ViewBuilder
    private func getColorForTab(_ tab: HistoryMode) -> some View {
        switch tab {
        case .image:
            ImageHistoryView(isEditing: $isEditing, selectedItems: $selectedItems)
        case .video:
            VideoHistoryView(isEditing: $isEditing, selectedItems: $selectedItems)
        case .effect:
            EffectHistoryView(isEditing: $isEditing, selectedItems: $selectedItems)
        }
    }
        
}


struct CustomNavigationBarView1: View {
    @EnvironmentObject var navigator: FlowPathNavigator

    @Binding var isEditing: Bool
    @Binding var selectedItems: Set<ArtWorksCell.ID>
    
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Image(systemName: "chevron.backward")
                    .foregroundStyle(DesignSystem.Colors.textPrimary)
                    .font(.title3)
                    .onTapGesture {
                        navigator.pop()
                    }
                Spacer()

                Button(isEditing ? "取消" : "选择") {
                    isEditing.toggle()
                    // 进入或退出编辑模式时，清空已选项
                    selectedItems.removeAll()
                }
            }
            .frame(height: 10)
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,20)
        .padding(.horizontal,15)
    }
}
