import FlowStacks
import SwiftUI

struct MyWorks: View {
    @State private var selectedWorkType: HistoryMode
    @EnvironmentObject var navigator: FlowPathNavigator
    private let authStore = AuthStore.shared
    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    init(defaultModel: HistoryMode? = .image) {
        _selectedWorkType = State(initialValue: defaultModel ?? .image)
    }

    var body: some View {
        VStack {
            HStack {
                Text("我的作品")
                
                Spacer()
                
                Button(action: {
                    if authStore.getAccessToken() == nil {
                        //显示登录弹窗（页面）
                        globalAuthManager.requestAuthentication()
                    }else {
                        navigator.push(Route.artWorkHistory(defaultMode: selectedWorkType))
                    }
                }) {
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        Text("管理")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        Image(systemName: "chevron.right")
                            .font(.system(size: 10))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                .padding(.trailing, DesignSystem.Spacing.sm)
            }
            
            ScrollViewReader { scrollViewProxy in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 10) {
                        ForEach(HistoryMode.allCases, id: \.self) { tab in
                            WorkTypeButton(title: tab.rawValue, isSelected: selectedWorkType == tab) {
                                withAnimation(.spring()) {
                                    selectedWorkType = tab
                                }
                            }
                            .id(tab)
                        }
                    }
                }
                .frame(height: 35)
                .background(Color.clear)
                .onChange(of: selectedWorkType) { newTab in
                    withAnimation {
                        scrollViewProxy.scrollTo(newTab, anchor: .center)
                    }
                }
                .padding(.bottom, 5)
            }
            
            TabView(selection: $selectedWorkType) {
                ForEach(HistoryMode.allCases, id: \.self) { tab in
                    ZStack {
                        if authStore.getAccessToken() == nil {
                            VStack(spacing:0){
                                Image("IconIsEmpty")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 100,height: 100)
                                Text("暂无作品哦，快去创作吧！")
                                    .font(.system(size: DesignSystem.FontSize.sm))
                                    .foregroundColor(.secondary)
                            }
                            .ignoresSafeArea()
                        }else {
                            getColorForTab(tab)
                                .ignoresSafeArea()
                        }
                        
                    }
                    .scrollContentBackground(.hidden)
                    .tag(tab)
                }
            }
            .padding(.horizontal, -15)
            .tabViewStyle(.page(indexDisplayMode: .never))
            .animation(nil, value: selectedWorkType)
        }
        .padding(.top, 20)
    }

    @ViewBuilder
    private func getColorForTab(_ tab: HistoryMode) -> some View {
        switch tab {
        case .image:
            ImageHistoryView(isMyWork: true, isEditing: .constant(false), selectedItems: .constant([]))
        case .video:
            VideoHistoryView(isMyWork: true, isEditing: .constant(false), selectedItems: .constant([]))
        case .effect:
            EffectHistoryView(isMyWork: true, isEditing: .constant(false), selectedItems: .constant([]))
        }
    }
}

struct WorkTypeButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: 5) {
            // 模型名称
            Button(action: action) {
                Text("AI \(title)")
                    .font(.system(size: DesignSystem.FontSize.md))
                    .padding(.horizontal, DesignSystem.Spacing.md)
                    .padding(.vertical, DesignSystem.Spacing.xs)
                    .background(
                        isSelected ?
                        AnyView(
                            Image("WorkTypeButtonBackground")
                                .resizable()
                                .scaledToFill()
                        )
                        :
                            (
                                colorScheme == .light ? AnyView(Color(hex: "#E7E7FB")) : AnyView(DesignSystem.Colors.backgroundCard)
                            )
                    )
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
                    .foregroundColor(
                        isSelected ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary
                    )
                    .cornerRadius(14)
                    .multilineTextAlignment(.center)
            }
        }
    }
}
