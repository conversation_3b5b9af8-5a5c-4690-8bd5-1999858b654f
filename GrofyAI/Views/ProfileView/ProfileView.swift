import FlowStacks
import SwiftUI

// MARK: - 个人中心主界面

struct ProfileView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @Environment(\.colorScheme) private var colorScheme
    
    
    // 辅助函数：获取顶部安全区域高度
    private func safeAreaTopInset() -> CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter({ $0.activationState == .foregroundActive })
            .map({ $0 as? UIWindowScene })
            .compactMap({ $0 })
            .first?.windows
            .filter({ $0.isKeyWindow }).first
        let safeAreaInsets = keyWindow?.safeAreaInsets.top  ?? 0
        
        if safeAreaInsets == 0 {
            return safeAreaInsets
        }
        return colorScheme == .light ? safeAreaInsets : (safeAreaInsets - 12)
    }
    
    
    
    var body: some View {
        GeometryReader { geometry in
            ZStack{
                if colorScheme == .light {
                    Color.clear
                        .background(
                            Image("ProfileBackground")
                                .resizable() // 关键：使图片可缩放
                                .scaledToFill() // 填充整个区域
                                .edgesIgnoringSafeArea(.all)
                        )
                        .frame(maxWidth:.infinity,maxHeight: .infinity)
                        
                }
                
                
                VStack(spacing: 0){
                    //安全区域高度
                    Color.clear.frame(height:  safeAreaTopInset() )
                    
                    VStack{
                        ProfileNavigationBarView()
                        
                        ScrollView(.vertical,showsIndicators: false){
                            UserInfoCard()
                            
                            MyWorks()
                                .frame(minHeight:UIScreen.main.bounds.height * 0.8)
                        }
                    }
                }
                .padding(.horizontal,15)
                .edgesIgnoringSafeArea(.all)
            }
            .navigationBarHidden(true)
        }
        .background(DesignSystem.Colors.backgroundPage)
    }
}


struct ProfileNavigationBarView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏主体
            HStack {
                Spacer()
                Image("IconSettings")
                    .renderingMode(.template)
//                    .font(.system(size: 24))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .onTapGesture {
                        navigator.push(Route.settings)
                    }
            }
            .frame(height: 30)
        }
        .frame(maxWidth: .infinity)
        .padding(.top,10)
        .padding(.bottom,5)
        
    }
}
