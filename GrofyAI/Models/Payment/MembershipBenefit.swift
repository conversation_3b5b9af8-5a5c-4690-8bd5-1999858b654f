import Foundation

// MARK: - 会员权益模型

struct MembershipBenefit {
    let title: String
    let badge: String?

    static let baseBenefits = [
        MembershipBenefit(title: "无限聊天消息", badge: "仅限移动设备"),
        MembershipBenefit(title: "回答来自", badge: nil),
        MembershipBenefit(title: "图像生成", badge: nil),
        MembershipBenefit(title: "智能总结文件 & 网页", badge: nil),
    ]

    static let proBenefits = baseBenefits
    static let plusBenefits = baseBenefits
    static let ultraBenefits = baseBenefits
}

// MARK: - 会员类型配置

enum MembershipType: String, CaseIterable {
    case free = "免费版"
    case vip = "高级版"
    case pro = "专业版"
    case plus = "进阶版"
    case ultra = "至尊版"

    var benefits: [MembershipBenefit] {
        switch self {
        case .free, .vip:
            return []
        case .pro:
            return MembershipBenefit.proBenefits
        case .plus:
            return MembershipBenefit.plusBenefits
        case .ultra:
            return MembershipBenefit.ultraBenefits
        }
    }

    var themeColor: String {
        switch self {
        case .free:
            return "#9CA3AF"
        case .vip:
            return "#637DFE"
        case .pro:
            return "#8339FF"
        case .plus:
            return "#FF6B6B"
        case .ultra:
            return "#A78BFA"
        }
    }

    var gradientColors: [String] {
        switch self {
        case .free:
            return ["#9CA3AF", "#D1D5DB", "#E5E7EB"]
        case .vip:
            return ["#637DFE", "#8194FF", "#A0B1FF"]
        case .pro:
            return ["#8339FF", "#9E5FFF", "#B985FF"]
        case .plus:
            return ["#FF6B6B", "#FF8787", "#FFA3A3"]
        case .ultra:
            return ["#A78BFA", "#8B5CF6", "#7C3AED"]
        }
    }

    var productType: String {
        switch self {
        case .free:
            return "FREE"
        case .vip:
            return "VIP"
        case .pro:
            return "PRO"
        case .plus:
            return "PLUS"
        case .ultra:
            return "ULTRA"
        }
    }

    var displayName: String {
        switch self {
        case .free:
            return "免费用户"
        case .vip:
            return "高级会员"
        case .pro:
            return "专业会员"
        case .plus:
            return "进阶会员"
        case .ultra:
            return "至尊会员"
        }
    }

    var description: String {
        switch self {
        case .free:
            return "基础功能体验"
        case .vip:
            return "解锁基础付费功能"
        case .pro:
            return "适合专业用户使用"
        case .plus:
            return "解锁更多高级功能"
        case .ultra:
            return "享受最顶级的服务"
        }
    }

    /// 根据会员等级字符串获取类型
    static func from(_ levelString: String?) -> MembershipType {
        guard let levelString else { return .free }

        switch levelString.uppercased() {
        case "FREE":
            return .free
        case "VIP":
            return .vip
        case "PRO":
            return .pro
        case "PLUS":
            return .plus
        case "ULTRA":
            return .ultra
        default:
            return .free
        }
    }

    /// 是否为可购买的会员类型
    var isPurchasable: Bool {
        switch self {
        case .plus, .pro, .ultra:
            return true
        case .free, .vip:
            return false
        }
    }
}
