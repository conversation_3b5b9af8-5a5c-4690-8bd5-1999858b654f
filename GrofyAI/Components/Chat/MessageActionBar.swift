import SwiftUI

// MARK: - 消息操作栏组件

/// 消息气泡底部的操作按钮栏
/// 包含重试、复制、分享、TTS等功能按钮
struct MessageActionBar: View {
    let message: ChatMessageModel
    let onRetry: (() -> Void)?
    let onCopy: () -> Void
    let onShare: () -> Void

    @EnvironmentObject private var ttsService: TextToSpeechService

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Spacer()

            // 文字转语音按钮
            if !message.isUser, !message.content.isEmpty {
                TTSMessageButton(
                    messageId: message.id.uuidString,
                    text: message.content,
                    ttsService: ttsService
                )
            }

            // 重试按钮 (仅AI消息显示)
            if !message.isUser, let onRetry {
                MessageActionButton(icon: "IconMessageRetry", isSystemIcon: false, action: onRetry)
            }

            // 复制按钮
            MessageActionButton(icon: "IconMessageCopy", isSystemIcon: false, action: onCopy)

            // 分享按钮
            MessageActionButton(icon: "IconMessageShare", isSystemIcon: false, action: onShare)
        }
        .opacity(0.8)
    }
}

// MARK: - 操作处理扩展

extension MessageActionBar {
    static func extractMainContent(from message: ChatMessageModel) -> String {
        if !message.isUser {
            return message.content
        }
        return message.content
    }

    static func formatMessageForSharing(_ message: ChatMessageModel) -> String {
        let role = message.isUser ? "用户" : "AI"
        let content = extractMainContent(from: message)

        var shareText = "[\(role)] \(content)"

        // 如果是AI消息且有模型信息，添加模型名称
        if !message.isUser, let modelId = message.modelId {
            if let modelInfo = ModelManager.shared.getModelById(modelId) {
                shareText = "[\(role) - \(modelInfo.safeName)] \(content)"
            }
        }

        shareText += "\n---\nFrom MoonvyAI"

        return shareText
    }
}
