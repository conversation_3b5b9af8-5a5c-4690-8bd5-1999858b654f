//
//  DalleImageReq.swift
//  GrofyAI
//
//  Created by kissy on 2025/6/19.
//

//MARK: OpenAiImageReq
struct OpenAiImageReq : Encodable{
    let threadId: String
    //提示词
    let prompt: String
    //图片宽高比,可用值 RATIO_1_1, RATIO_4_7, RATIO_7_4,
    let background: OpenAiImageAi.Background.RawValue?
    let model: OpenAiImageAi.Model.RawValue
    let size: OpenAiImageAi.Size.RawValue
    //图片质量(图片尺寸):STANDARD标准、HD高清, 可用值:STANDARD, HD
    let quality: OpenAiImageAi.Quality.RawValue
    //图片风格:NATURAL自然、VIVID生动,可用值: NATURAL, VIVID,
    let style: OpenAiImageAi.Style.RawValue
}
